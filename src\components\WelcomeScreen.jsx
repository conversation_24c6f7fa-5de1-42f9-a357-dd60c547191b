import React from 'react'
import { motion } from 'framer-motion'
import { FaHeart, FaSparkles } from 'react-icons/fa'

const WelcomeScreen = ({ onStart }) => {
  const cardVariants = {
    initial: { scale: 0.8, opacity: 0 },
    animate: { 
      scale: 1, 
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  const titleVariants = {
    initial: { y: -30, opacity: 0 },
    animate: { 
      y: 0, 
      opacity: 1,
      transition: {
        delay: 0.2,
        duration: 0.8,
        ease: "easeOut"
      }
    }
  }

  const buttonVariants = {
    initial: { y: 20, opacity: 0 },
    animate: { 
      y: 0, 
      opacity: 1,
      transition: {
        delay: 0.6,
        duration: 0.6,
        ease: "easeOut"
      }
    },
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.2
      }
    },
    tap: {
      scale: 0.95
    }
  }

  return (
    <div className="screen-container">
      <div className="welcome-container">
        <motion.h1 
          className="welcome-title title-script"
          variants={titleVariants}
          initial="initial"
          animate="animate"
        >
          Our Little Love Quest 💌
        </motion.h1>
        
        <motion.p 
          className="welcome-subtitle text-romantic"
          variants={titleVariants}
          initial="initial"
          animate="animate"
          style={{ transitionDelay: '0.4s' }}
        >
          A magical journey through love and memories...
        </motion.p>

        <motion.div 
          className="card-romantic"
          variants={cardVariants}
          initial="initial"
          animate="animate"
        >
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.6 }}
          >
            <h2 className="title-elegant" style={{ fontSize: '2rem', marginBottom: '24px', color: 'var(--text-primary)' }}>
              Hey Beautiful! 💕
            </h2>
            
            <p className="text-romantic" style={{ fontSize: '1.1rem', marginBottom: '32px', lineHeight: '1.7' }}>
              Someone special has created this little love quest just for you. 
              Are you ready to embark on a journey through sweet memories and romantic questions?
            </p>

            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '12px', marginBottom: '32px' }}>
              <FaSparkles style={{ color: 'var(--lavender-400)', fontSize: '1.5rem' }} />
              <span style={{ color: 'var(--text-secondary)', fontStyle: 'italic' }}>
                Your answers will unlock beautiful surprises...
              </span>
              <FaSparkles style={{ color: 'var(--lavender-400)', fontSize: '1.5rem' }} />
            </div>

            <motion.button
              className="btn-romantic"
              onClick={onStart}
              variants={buttonVariants}
              initial="initial"
              animate="animate"
              whileHover="hover"
              whileTap="tap"
            >
              <FaHeart />
              Start the Quest
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}

export default WelcomeScreen
