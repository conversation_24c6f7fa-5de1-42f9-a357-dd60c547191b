{"name": "our-little-love-quest", "version": "1.0.0", "description": "A romantic quiz app with glassmorphism design and lavender theme", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "vite"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.4", "react-confetti": "^6.1.0", "react-icons": "^4.11.0", "axios": "^1.5.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "vite": "^4.4.5"}, "keywords": ["react", "romantic", "quiz", "glassmorphism", "lavender"], "author": "Your Secret Admirer", "license": "MIT"}