import React from 'react'
import { motion } from 'framer-motion'

const FloatingHearts = () => {
  const hearts = ['💕', '💖', '💗', '💝', '💘', '💞', '💓', '💜']

  const heartVariants = {
    animate: (index) => ({
      y: [window.innerHeight + 50, -100],
      x: [0, Math.sin(index) * 50, Math.cos(index) * 30, 0],
      rotate: [0, 360],
      scale: [0.8, 1.2, 0.8],
      opacity: [0, 0.7, 0.7, 0],
      transition: {
        duration: 8 + (index % 3),
        repeat: Infinity,
        delay: index * 1.5,
        ease: "linear"
      }
    })
  }

  return (
    <div className="floating-hearts">
      {hearts.map((heart, index) => (
        <motion.div
          key={index}
          className="floating-heart"
          variants={heartVariants}
          animate="animate"
          custom={index}
          style={{
            position: 'absolute',
            left: `${10 + (index * 11)}%`,
            fontSize: '24px',
            userSelect: 'none',
            pointerEvents: 'none',
            filter: 'drop-shadow(0 0 10px rgba(168, 85, 247, 0.3))'
          }}
        >
          {heart}
        </motion.div>
      ))}
    </div>
  )
}

export default FloatingHearts
