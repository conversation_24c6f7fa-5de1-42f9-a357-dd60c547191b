export const questionsData = {
  initial: [
    {
      id: 'q1',
      text: "What's your favorite nickname that someone special calls you? 💕",
      type: 'text',
      category: 'light'
    },
    {
      id: 'q2',
      text: "If you could describe your perfect evening in three words, what would they be?",
      type: 'text',
      category: 'light'
    },
    {
      id: 'q3',
      text: "What's something that always makes you smile, no matter what? 😊",
      type: 'multiple-choice',
      options: [
        "A sweet text message",
        "Surprise flowers or gifts",
        "Quality time with someone special",
        "Acts of kindness",
        "Beautiful sunsets or nature"
      ],
      category: 'light'
    },
    {
      id: 'q4',
      text: "What's your favorite way to spend a lazy Sunday?",
      type: 'multiple-choice',
      options: [
        "Cuddling and watching movies",
        "Going for a peaceful walk",
        "Reading a good book",
        "Cooking something delicious together",
        "Just talking and laughing"
      ],
      category: 'light'
    },
    // Milestone 1 - Surprise screen appears here
    {
      id: 'q5',
      text: "What's the most romantic gesture someone has ever done for you? 💖",
      type: 'textarea',
      category: 'romantic'
    },
    {
      id: 'q6',
      text: "When you think about love, what's the first feeling that comes to mind?",
      type: 'multiple-choice',
      options: [
        "Warmth and comfort",
        "Excitement and butterflies",
        "Peace and contentment",
        "Adventure and growth",
        "Safety and trust"
      ],
      category: 'romantic'
    },
    {
      id: 'q7',
      text: "What's your favorite memory from college days? 🎓",
      type: 'textarea',
      category: 'memories'
    },
    {
      id: 'q8',
      text: "If someone wanted to make you feel truly special, what would they do?",
      type: 'textarea',
      category: 'romantic'
    },
    // Milestone 2 - Surprise screen appears here
    {
      id: 'q9',
      text: "What's something about yourself that you hope someone special notices? ✨",
      type: 'textarea',
      category: 'deep'
    },
    {
      id: 'q10',
      text: "When did you first realize that friendship could turn into something deeper?",
      type: 'textarea',
      category: 'deep'
    },
    {
      id: 'q11',
      text: "What's your idea of the perfect date?",
      type: 'multiple-choice',
      options: [
        "A quiet dinner with deep conversations",
        "An adventure or trying something new together",
        "A cozy movie night at home",
        "A romantic walk under the stars",
        "Visiting a special place with meaning"
      ],
      category: 'romantic'
    },
    {
      id: 'q12',
      text: "What's one thing you've always wanted to tell someone but haven't found the courage to say? 💭",
      type: 'textarea',
      category: 'deep'
    },
    // Milestone 3 - Surprise screen appears here
    {
      id: 'q13',
      text: "How do you know when someone truly cares about you?",
      type: 'textarea',
      category: 'deep'
    },
    {
      id: 'q14',
      text: "What's your favorite thing about the person who's always there for you? 💕",
      type: 'textarea',
      category: 'deep'
    },
    {
      id: 'q15',
      text: "If you could go back to any moment in your life and live it again, which would it be?",
      type: 'textarea',
      category: 'memories'
    },
    {
      id: 'q16',
      text: "What's something you hope for in your future that makes your heart flutter? 🦋",
      type: 'textarea',
      category: 'deep'
    }
  ],

  // Dynamic follow-up questions based on answers
  followUps: {
    nickname: [
      {
        id: 'followup_nickname',
        text: "What makes that nickname so special when they say it? 💫",
        type: 'textarea'
      }
    ],
    college: [
      {
        id: 'followup_college',
        text: "Who made those college days even more memorable? 😊",
        type: 'text'
      }
    ],
    friendship: [
      {
        id: 'followup_friendship',
        text: "What was the exact moment you felt that shift from friendship to something more? 💕",
        type: 'textarea'
      }
    ]
  },

  // Surprise messages for milestones
  surprises: [
    {
      title: "You're Amazing! 💖",
      message: "Your answers are making this journey so special. Every word you share brings more magic to this quest..."
    },
    {
      title: "Beautiful Soul! ✨", 
      message: "The way you express yourself is absolutely enchanting. Someone is falling deeper with every answer..."
    },
    {
      title: "Incredible! 💫",
      message: "Your thoughts and feelings are painting the most beautiful picture. This journey is becoming more magical because of you..."
    }
  ]
}

// Helper function to get next question based on previous answers
export const getNextQuestion = (currentIndex, answers) => {
  const baseQuestions = questionsData.initial
  
  // Simple linear progression for now
  // You can add more complex logic here for dynamic follow-ups
  if (currentIndex < baseQuestions.length) {
    return baseQuestions[currentIndex]
  }
  
  return null
}

// Helper function to determine if a surprise should be shown
export const shouldShowSurprise = (questionCount) => {
  return questionCount > 0 && questionCount % 4 === 0
}

// Helper function to get surprise message
export const getSurpriseMessage = (milestone) => {
  const surprises = questionsData.surprises
  return surprises[milestone % surprises.length]
}
