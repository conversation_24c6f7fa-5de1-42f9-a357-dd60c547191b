/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* Floating Hearts Animation */
.hearts-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.heart {
    position: absolute;
    font-size: 20px;
    animation: float 6s ease-in-out infinite;
    opacity: 0.7;
}

.heart-1 { left: 10%; animation-delay: 0s; }
.heart-2 { left: 20%; animation-delay: 1s; }
.heart-3 { left: 30%; animation-delay: 2s; }
.heart-4 { left: 70%; animation-delay: 3s; }
.heart-5 { left: 80%; animation-delay: 4s; }
.heart-6 { left: 90%; animation-delay: 5s; }

@keyframes float {
    0%, 100% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
    10%, 90% { opacity: 0.7; }
    50% { transform: translateY(-10vh) rotate(180deg); opacity: 1; }
}

/* Main Container */
.container {
    position: relative;
    z-index: 2;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* Screen Management */
.screen {
    display: none;
    width: 100%;
    max-width: 600px;
    animation: fadeIn 0.8s ease-in-out;
}

.screen.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Typography */
.main-title {
    font-family: 'Dancing Script', cursive;
    font-size: 3.5rem;
    font-weight: 700;
    color: #d63384;
    text-align: center;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.subtitle {
    font-size: 1.2rem;
    color: #6f42c1;
    text-align: center;
    margin-bottom: 30px;
    font-weight: 300;
}

/* Heart Cards */
.heart-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 40px 30px;
    box-shadow: 0 20px 40px rgba(214, 51, 132, 0.2);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 182, 193, 0.3);
    position: relative;
    overflow: hidden;
    animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 20px 40px rgba(214, 51, 132, 0.2); }
    to { box-shadow: 0 25px 50px rgba(214, 51, 132, 0.4); }
}

.heart-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 182, 193, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s linear infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.heart-card h2 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.2rem;
    color: #d63384;
    text-align: center;
    margin-bottom: 20px;
}

.heart-card p {
    color: #6f42c1;
    line-height: 1.6;
    text-align: center;
    margin-bottom: 25px;
    font-size: 1.1rem;
}

/* Romantic Button */
.romantic-btn {
    background: linear-gradient(135deg, #ff6b9d, #d63384);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: block;
    margin: 0 auto;
    box-shadow: 0 10px 20px rgba(214, 51, 132, 0.3);
}

.romantic-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(214, 51, 132, 0.4);
    background: linear-gradient(135deg, #d63384, #ff6b9d);
}

.romantic-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.romantic-btn i {
    margin-right: 8px;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 182, 193, 0.3);
    border-radius: 10px;
    margin-bottom: 20px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff6b9d, #d63384);
    border-radius: 10px;
    width: 0%;
    transition: width 0.5s ease;
}

/* Question Counter */
.question-counter {
    text-align: center;
    color: #6f42c1;
    font-weight: 500;
    margin-bottom: 25px;
    font-size: 1.1rem;
}

/* Answer Inputs */
.answer-input {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid rgba(255, 182, 193, 0.5);
    border-radius: 15px;
    font-size: 1rem;
    margin-bottom: 15px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
}

.answer-input:focus {
    outline: none;
    border-color: #d63384;
    box-shadow: 0 0 15px rgba(214, 51, 132, 0.2);
    background: rgba(255, 255, 255, 0.95);
}

.answer-options {
    display: grid;
    gap: 10px;
    margin-bottom: 20px;
}

.option-btn {
    padding: 15px 20px;
    border: 2px solid rgba(255, 182, 193, 0.5);
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.8);
    color: #6f42c1;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    text-align: left;
}

.option-btn:hover {
    border-color: #d63384;
    background: rgba(255, 182, 193, 0.2);
}

.option-btn.selected {
    background: linear-gradient(135deg, #ff6b9d, #d63384);
    color: white;
    border-color: #d63384;
}

/* Surprise Screen */
.surprise-animation {
    text-align: center;
    font-size: 3rem;
    color: #ffd700;
    margin: 20px 0;
    animation: sparkle 1.5s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.2) rotate(180deg); }
}

/* Final Screen */
.final-animation {
    text-align: center;
    margin: 30px 0;
}

.confetti {
    font-size: 2rem;
    animation: confetti-fall 3s ease-in-out infinite;
}

.sparkles {
    font-size: 1.5rem;
    margin-top: 10px;
    animation: twinkle 2s ease-in-out infinite;
}

@keyframes confetti-fall {
    0% { transform: translateY(-20px) rotate(0deg); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateY(20px) rotate(360deg); opacity: 0; }
}

@keyframes twinkle {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

.mystery-signature {
    font-family: 'Dancing Script', cursive;
    font-size: 1.5rem;
    color: #d63384;
    text-align: center;
    margin-top: 30px;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-title {
        font-size: 2.5rem;
    }
    
    .heart-card {
        padding: 30px 20px;
    }
    
    .container {
        padding: 15px;
    }
}
