/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Lavender Color Palette */
  --lavender-50: #faf7ff;
  --lavender-100: #f3edff;
  --lavender-200: #e9ddff;
  --lavender-300: #d8c4ff;
  --lavender-400: #c29dff;
  --lavender-500: #a855f7;
  --lavender-600: #9333ea;
  --lavender-700: #7c2d12;
  --lavender-800: #6b21a8;
  --lavender-900: #581c87;
  
  /* Glass Colors */
  --glass-white: rgba(255, 255, 255, 0.25);
  --glass-lavender: rgba(168, 85, 247, 0.15);
  --glass-border: rgba(255, 255, 255, 0.3);
  --glass-shadow: rgba(168, 85, 247, 0.2);
  
  /* Text Colors */
  --text-primary: #4c1d95;
  --text-secondary: #6b21a8;
  --text-accent: #a855f7;
  --text-light: rgba(76, 29, 149, 0.8);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, 
    #faf7ff 0%, 
    #f3edff 25%, 
    #e9ddff 50%, 
    #d8c4ff 75%, 
    #c29dff 100%);
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

/* Glassmorphism Base Class */
.glass {
  background: var(--glass-white);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px var(--glass-shadow);
}

.glass-lavender {
  background: var(--glass-lavender);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(168, 85, 247, 0.2);
  box-shadow: 0 12px 40px rgba(168, 85, 247, 0.3);
}

/* Typography */
.title-script {
  font-family: 'Dancing Script', cursive;
  font-weight: 700;
  color: var(--text-primary);
  text-shadow: 2px 2px 4px rgba(168, 85, 247, 0.1);
}

.title-elegant {
  font-family: 'Playfair Display', serif;
  font-weight: 600;
  color: var(--text-secondary);
}

.text-romantic {
  color: var(--text-light);
  line-height: 1.6;
  font-weight: 400;
}

/* Button Styles */
.btn-romantic {
  background: linear-gradient(135deg, var(--lavender-400), var(--lavender-500));
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(168, 85, 247, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.btn-romantic::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-romantic:hover::before {
  left: 100%;
}

.btn-romantic:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(168, 85, 247, 0.4);
  background: linear-gradient(135deg, var(--lavender-500), var(--lavender-600));
}

.btn-romantic:active {
  transform: translateY(0);
}

.btn-romantic:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Input Styles */
.input-glass {
  background: var(--glass-white);
  backdrop-filter: blur(15px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 16px 20px;
  font-size: 1rem;
  color: var(--text-primary);
  width: 100%;
  transition: all 0.3s ease;
  font-family: 'Inter', sans-serif;
}

.input-glass::placeholder {
  color: var(--text-light);
}

.input-glass:focus {
  outline: none;
  border-color: var(--lavender-400);
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.2);
  background: rgba(255, 255, 255, 0.35);
}

/* Card Styles */
.card-glass {
  background: var(--glass-white);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  padding: 40px 32px;
  box-shadow: 0 16px 50px var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.card-glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

.card-romantic {
  background: var(--glass-lavender);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border: 1px solid rgba(168, 85, 247, 0.25);
  border-radius: 28px;
  padding: 48px 36px;
  box-shadow: 0 20px 60px rgba(168, 85, 247, 0.25);
  position: relative;
  overflow: hidden;
}

.card-romantic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
}

/* Progress Bar */
.progress-container {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--lavender-400), var(--lavender-500));
  border-radius: 10px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 15px rgba(168, 85, 247, 0.5);
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 16px 50px var(--glass-shadow); }
  50% { box-shadow: 0 20px 60px rgba(168, 85, 247, 0.4); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-glass,
  .card-romantic {
    padding: 32px 24px;
    border-radius: 20px;
  }
  
  .btn-romantic {
    padding: 14px 28px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .card-glass,
  .card-romantic {
    padding: 24px 20px;
    border-radius: 16px;
  }
}
