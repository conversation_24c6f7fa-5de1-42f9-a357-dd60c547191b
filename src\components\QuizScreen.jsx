import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { FaArrowRight, FaHeart } from 'react-icons/fa'

const QuizScreen = ({ question, questionNumber, totalQuestions, onAnswerSubmit, progress }) => {
  const [selectedAnswer, setSelectedAnswer] = useState('')
  const [isAnswered, setIsAnswered] = useState(false)

  useEffect(() => {
    setSelectedAnswer('')
    setIsAnswered(false)
  }, [question])

  const handleAnswerSelect = (answer) => {
    setSelectedAnswer(answer)
    setIsAnswered(true)
  }

  const handleSubmit = () => {
    if (selectedAnswer) {
      onAnswerSubmit(selectedAnswer)
    }
  }

  const cardVariants = {
    initial: { scale: 0.9, opacity: 0, y: 20 },
    animate: { 
      scale: 1, 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  }

  const optionVariants = {
    initial: { x: -20, opacity: 0 },
    animate: (index) => ({
      x: 0,
      opacity: 1,
      transition: {
        delay: index * 0.1,
        duration: 0.4,
        ease: "easeOut"
      }
    }),
    hover: {
      scale: 1.02,
      transition: { duration: 0.2 }
    },
    tap: {
      scale: 0.98
    }
  }

  const buttonVariants = {
    initial: { y: 20, opacity: 0 },
    animate: { 
      y: 0, 
      opacity: 1,
      transition: {
        delay: 0.6,
        duration: 0.4
      }
    },
    hover: {
      scale: 1.05,
      transition: { duration: 0.2 }
    },
    tap: {
      scale: 0.95
    }
  }

  return (
    <div className="screen-container">
      <div className="quiz-container">
        <motion.div 
          className="quiz-header"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="progress-container">
            <motion.div 
              className="progress-fill"
              initial={{ width: 0 }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            />
          </div>
          
          <div className="question-counter">
            <motion.span
              key={questionNumber}
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              Question {questionNumber} of {totalQuestions}
            </motion.span>
          </div>
        </motion.div>

        <motion.div 
          className="card-romantic"
          variants={cardVariants}
          initial="initial"
          animate="animate"
          key={question?.id}
        >
          <motion.h2 
            className="question-title title-elegant"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            {question?.text}
          </motion.h2>

          <div className="answer-container">
            {question?.type === 'multiple-choice' && (
              <div className="answer-options">
                <AnimatePresence>
                  {question.options.map((option, index) => (
                    <motion.button
                      key={option}
                      className={`option-button ${selectedAnswer === option ? 'selected' : ''}`}
                      onClick={() => handleAnswerSelect(option)}
                      variants={optionVariants}
                      initial="initial"
                      animate="animate"
                      whileHover="hover"
                      whileTap="tap"
                      custom={index}
                    >
                      {option}
                    </motion.button>
                  ))}
                </AnimatePresence>
              </div>
            )}

            {question?.type === 'text' && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.5 }}
              >
                <input
                  type="text"
                  className="input-glass"
                  placeholder="Type your answer here..."
                  value={selectedAnswer}
                  onChange={(e) => {
                    setSelectedAnswer(e.target.value)
                    setIsAnswered(e.target.value.trim() !== '')
                  }}
                  style={{ marginBottom: '20px' }}
                />
              </motion.div>
            )}

            {question?.type === 'textarea' && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.5 }}
              >
                <textarea
                  className="input-glass"
                  placeholder="Share your thoughts..."
                  value={selectedAnswer}
                  onChange={(e) => {
                    setSelectedAnswer(e.target.value)
                    setIsAnswered(e.target.value.trim() !== '')
                  }}
                  rows={4}
                  style={{ 
                    marginBottom: '20px',
                    resize: 'vertical',
                    minHeight: '100px'
                  }}
                />
              </motion.div>
            )}
          </div>

          <motion.button
            className="btn-romantic"
            onClick={handleSubmit}
            disabled={!isAnswered}
            variants={buttonVariants}
            initial="initial"
            animate="animate"
            whileHover={isAnswered ? "hover" : {}}
            whileTap={isAnswered ? "tap" : {}}
          >
            <FaHeart />
            Continue
            <FaArrowRight />
          </motion.button>
        </motion.div>
      </div>
    </div>
  )
}

export default QuizScreen
