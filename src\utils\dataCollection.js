import axios from 'axios'

// Configuration for different data collection methods
const DATA_COLLECTION_CONFIG = {
  method: 'formspree', // Options: 'formspree', 'google-sheets', 'email'
  
  // Formspree configuration (easiest setup)
  formspree: {
    endpoint: 'https://formspree.io/f/YOUR_FORM_ID', // Replace with your Formspree form ID
  },
  
  // Google Sheets configuration (more advanced)
  googleSheets: {
    scriptUrl: 'YOUR_GOOGLE_APPS_SCRIPT_URL', // Replace with your Google Apps Script URL
    sheetId: 'YOUR_SHEET_ID'
  },
  
  // Email configuration (using EmailJS)
  email: {
    serviceId: 'YOUR_EMAILJS_SERVICE_ID',
    templateId: 'YOUR_EMAILJS_TEMPLATE_ID',
    publicKey: 'YOUR_EMAILJS_PUBLIC_KEY'
  }
}

/**
 * Submit answer to your chosen data collection service
 * @param {Object} answerData - The answer data to submit
 * @param {string} answerData.questionId - Unique question identifier
 * @param {string} answerData.question - The question text
 * @param {string} answerData.answer - The user's answer
 * @param {string} answerData.timestamp - When the answer was given
 */
export const submitAnswerToSheet = async (answerData) => {
  try {
    const method = DATA_COLLECTION_CONFIG.method
    
    switch (method) {
      case 'formspree':
        return await submitToFormspree(answerData)
      
      case 'google-sheets':
        return await submitToGoogleSheets(answerData)
      
      case 'email':
        return await submitToEmail(answerData)
      
      default:
        console.log('Answer collected locally:', answerData)
        return { success: true, method: 'local' }
    }
  } catch (error) {
    console.error('Failed to submit answer:', error)
    // Store locally as backup
    storeAnswerLocally(answerData)
    throw error
  }
}

/**
 * Submit to Formspree (Recommended - Easy Setup)
 * 1. Go to https://formspree.io/
 * 2. Create a free account
 * 3. Create a new form
 * 4. Replace YOUR_FORM_ID with your actual form ID
 */
const submitToFormspree = async (answerData) => {
  const endpoint = DATA_COLLECTION_CONFIG.formspree.endpoint
  
  if (endpoint.includes('YOUR_FORM_ID')) {
    console.log('Formspree not configured. Answer stored locally:', answerData)
    storeAnswerLocally(answerData)
    return { success: true, method: 'local' }
  }
  
  const response = await axios.post(endpoint, {
    questionId: answerData.questionId,
    question: answerData.question,
    answer: answerData.answer,
    timestamp: answerData.timestamp,
    subject: `New Love Quest Answer: ${answerData.questionId}`,
    message: `Question: ${answerData.question}\n\nAnswer: ${answerData.answer}\n\nTimestamp: ${answerData.timestamp}`
  }, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
  
  return { success: true, method: 'formspree', response: response.data }
}

/**
 * Submit to Google Sheets (Advanced Setup)
 * Requires setting up Google Apps Script
 */
const submitToGoogleSheets = async (answerData) => {
  const scriptUrl = DATA_COLLECTION_CONFIG.googleSheets.scriptUrl
  
  if (scriptUrl.includes('YOUR_GOOGLE_APPS_SCRIPT_URL')) {
    console.log('Google Sheets not configured. Answer stored locally:', answerData)
    storeAnswerLocally(answerData)
    return { success: true, method: 'local' }
  }
  
  const response = await axios.post(scriptUrl, answerData, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
  
  return { success: true, method: 'google-sheets', response: response.data }
}

/**
 * Submit via Email (using EmailJS)
 * Requires EmailJS setup
 */
const submitToEmail = async (answerData) => {
  // This would require EmailJS library
  // For now, just log and store locally
  console.log('Email method not implemented. Answer stored locally:', answerData)
  storeAnswerLocally(answerData)
  return { success: true, method: 'local' }
}

/**
 * Store answer locally as backup
 */
const storeAnswerLocally = (answerData) => {
  try {
    const existingAnswers = JSON.parse(localStorage.getItem('loveQuestAnswers') || '[]')
    existingAnswers.push(answerData)
    localStorage.setItem('loveQuestAnswers', JSON.stringify(existingAnswers))
    console.log('Answer stored locally as backup')
  } catch (error) {
    console.error('Failed to store answer locally:', error)
  }
}

/**
 * Get all locally stored answers (for debugging)
 */
export const getLocalAnswers = () => {
  try {
    return JSON.parse(localStorage.getItem('loveQuestAnswers') || '[]')
  } catch (error) {
    console.error('Failed to retrieve local answers:', error)
    return []
  }
}

/**
 * Clear all locally stored answers
 */
export const clearLocalAnswers = () => {
  try {
    localStorage.removeItem('loveQuestAnswers')
    console.log('Local answers cleared')
  } catch (error) {
    console.error('Failed to clear local answers:', error)
  }
}

// Setup instructions for different methods
export const SETUP_INSTRUCTIONS = {
  formspree: `
    FORMSPREE SETUP (Recommended - Easiest):
    1. Go to https://formspree.io/
    2. Sign up for a free account
    3. Create a new form
    4. Copy your form endpoint (looks like: https://formspree.io/f/xzbqklmn)
    5. Replace 'YOUR_FORM_ID' in the endpoint URL above
    6. All answers will be sent to your email automatically!
  `,
  
  googleSheets: `
    GOOGLE SHEETS SETUP (Advanced):
    1. Create a new Google Sheet
    2. Go to Extensions > Apps Script
    3. Create a script to receive POST requests and write to your sheet
    4. Deploy as web app
    5. Copy the web app URL and replace 'YOUR_GOOGLE_APPS_SCRIPT_URL'
  `,
  
  email: `
    EMAIL SETUP (Using EmailJS):
    1. Go to https://www.emailjs.com/
    2. Create account and set up email service
    3. Create email template
    4. Get your service ID, template ID, and public key
    5. Replace the configuration values above
  `
}

console.log('Data Collection Setup Instructions:', SETUP_INSTRUCTIONS.formspree)
