import React, { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FaVolumeUp, FaVolumeMute } from 'react-icons/fa'

const BackgroundMusic = () => {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(true)
  const audioRef = useRef(null)

  // You can replace this with your own romantic music URL
  const musicUrl = "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav" // Placeholder

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = 0.3 // Set volume to 30%
      audioRef.current.loop = true
    }
  }, [])

  const toggleMusic = () => {
    if (audioRef.current) {
      if (isMuted) {
        audioRef.current.play()
          .then(() => {
            setIsPlaying(true)
            setIsMuted(false)
          })
          .catch((error) => {
            console.log('Audio play failed:', error)
          })
      } else {
        audioRef.current.pause()
        setIsPlaying(false)
        setIsMuted(true)
      }
    }
  }

  const buttonVariants = {
    hover: {
      scale: 1.1,
      transition: { duration: 0.2 }
    },
    tap: {
      scale: 0.9
    }
  }

  return (
    <>
      <audio
        ref={audioRef}
        preload="auto"
        onEnded={() => setIsPlaying(false)}
      >
        {/* You can add your romantic music file here */}
        {/* <source src="/romantic-music.mp3" type="audio/mpeg" /> */}
      </audio>

      <motion.button
        className="music-control"
        onClick={toggleMusic}
        variants={buttonVariants}
        whileHover="hover"
        whileTap="tap"
        title={isMuted ? "Play romantic music" : "Pause music"}
      >
        <motion.div
          animate={{
            rotate: isPlaying ? [0, 360] : 0
          }}
          transition={{
            duration: 3,
            repeat: isPlaying ? Infinity : 0,
            ease: "linear"
          }}
        >
          {isMuted ? <FaVolumeMute /> : <FaVolumeUp />}
        </motion.div>
      </motion.button>
    </>
  )
}

export default BackgroundMusic
