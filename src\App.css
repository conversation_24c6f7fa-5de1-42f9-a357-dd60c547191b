.app {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.app-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  z-index: 10;
}

/* Screen Transitions */
.screen-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

/* Floating Hearts Background */
.floating-hearts {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.floating-heart {
  position: absolute;
  font-size: 24px;
  opacity: 0.6;
  animation: floatUp 8s linear infinite;
  color: var(--lavender-400);
  filter: drop-shadow(0 0 10px rgba(168, 85, 247, 0.3));
}

.floating-heart:nth-child(1) { left: 10%; animation-delay: 0s; }
.floating-heart:nth-child(2) { left: 20%; animation-delay: 1.5s; }
.floating-heart:nth-child(3) { left: 30%; animation-delay: 3s; }
.floating-heart:nth-child(4) { left: 40%; animation-delay: 4.5s; }
.floating-heart:nth-child(5) { left: 60%; animation-delay: 6s; }
.floating-heart:nth-child(6) { left: 70%; animation-delay: 7.5s; }
.floating-heart:nth-child(7) { left: 80%; animation-delay: 9s; }
.floating-heart:nth-child(8) { left: 90%; animation-delay: 10.5s; }

@keyframes floatUp {
  0% {
    transform: translateY(100vh) rotate(0deg) scale(0.8);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100px) rotate(360deg) scale(1.2);
    opacity: 0;
  }
}

/* Welcome Screen Specific */
.welcome-container {
  text-align: center;
}

.welcome-title {
  font-size: 3.5rem;
  margin-bottom: 16px;
  background: linear-gradient(135deg, var(--lavender-500), var(--lavender-700));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 1.3rem;
  margin-bottom: 40px;
  color: var(--text-secondary);
  font-weight: 300;
}

/* Quiz Screen Specific */
.quiz-container {
  width: 100%;
}

.quiz-header {
  margin-bottom: 32px;
}

.question-counter {
  text-align: center;
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: 20px;
  font-size: 1.1rem;
}

.question-title {
  font-size: 1.8rem;
  margin-bottom: 32px;
  text-align: center;
  line-height: 1.4;
}

.answer-container {
  margin-bottom: 32px;
}

.answer-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-button {
  background: var(--glass-white);
  backdrop-filter: blur(15px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 16px 20px;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 400;
}

.option-button:hover {
  background: var(--glass-lavender);
  border-color: var(--lavender-400);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(168, 85, 247, 0.2);
}

.option-button.selected {
  background: linear-gradient(135deg, var(--lavender-400), var(--lavender-500));
  color: white;
  border-color: var(--lavender-500);
  box-shadow: 0 8px 25px rgba(168, 85, 247, 0.4);
}

/* Surprise Screen */
.surprise-container {
  text-align: center;
}

.surprise-title {
  font-size: 2.5rem;
  margin-bottom: 24px;
  color: var(--text-primary);
}

.surprise-message {
  font-size: 1.2rem;
  margin-bottom: 40px;
  color: var(--text-secondary);
  line-height: 1.6;
}

.surprise-animation {
  font-size: 4rem;
  margin: 32px 0;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.8; }
  50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
}

/* Final Screen */
.final-container {
  text-align: center;
}

.final-title {
  font-size: 2.8rem;
  margin-bottom: 24px;
  color: var(--text-primary);
}

.final-message {
  font-size: 1.2rem;
  margin-bottom: 40px;
  color: var(--text-secondary);
  line-height: 1.7;
}

.final-signature {
  font-size: 1.4rem;
  margin-top: 40px;
  color: var(--lavender-600);
  font-style: italic;
}

.confetti-container {
  margin: 32px 0;
  font-size: 2rem;
  animation: confetti 3s ease-in-out infinite;
}

@keyframes confetti {
  0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.7; }
  25% { transform: translateY(-10px) rotate(90deg); opacity: 1; }
  50% { transform: translateY(0) rotate(180deg); opacity: 0.8; }
  75% { transform: translateY(-5px) rotate(270deg); opacity: 1; }
}

/* Music Control */
.music-control {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background: var(--glass-lavender);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(168, 85, 247, 0.3);
  border-radius: 50px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-primary);
  font-size: 1.2rem;
}

.music-control:hover {
  background: var(--glass-white);
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(168, 85, 247, 0.3);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .welcome-title {
    font-size: 2.8rem;
  }
  
  .question-title {
    font-size: 1.5rem;
  }
  
  .surprise-title,
  .final-title {
    font-size: 2.2rem;
  }
  
  .app-container {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .welcome-title {
    font-size: 2.2rem;
  }
  
  .question-title {
    font-size: 1.3rem;
  }
  
  .surprise-title,
  .final-title {
    font-size: 1.8rem;
  }
  
  .floating-heart {
    font-size: 20px;
  }
}
