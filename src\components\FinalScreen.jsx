import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import <PERSON><PERSON>tti from 'react-confetti'
import { FaH<PERSON>t, FaSparkles } from 'react-icons/fa'

const FinalScreen = ({ totalAnswers }) => {
  const [showCon<PERSON><PERSON>, setShow<PERSON><PERSON><PERSON><PERSON>] = useState(false)
  const [windowDimensions, setWindowDimensions] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  })

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowConfetti(true)
    }, 1000)

    const handleResize = () => {
      setWindowDimensions({
        width: window.innerWidth,
        height: window.innerHeight
      })
    }

    window.addEventListener('resize', handleResize)

    return () => {
      clearTimeout(timer)
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const containerVariants = {
    initial: { scale: 0.8, opacity: 0 },
    animate: { 
      scale: 1, 
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  }

  const titleVariants = {
    initial: { y: -30, opacity: 0 },
    animate: { 
      y: 0, 
      opacity: 1,
      transition: {
        delay: 0.3,
        duration: 0.8,
        ease: "easeOut"
      }
    }
  }

  const messageVariants = {
    initial: { opacity: 0 },
    animate: { 
      opacity: 1,
      transition: {
        delay: 0.6,
        duration: 0.8
      }
    }
  }

  const signatureVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: {
        delay: 1.2,
        duration: 0.8
      }
    }
  }

  const heartVariants = {
    animate: {
      scale: [1, 1.2, 1],
      rotate: [0, 10, -10, 0],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  return (
    <div className="screen-container">
      {showConfetti && (
        <Confetti
          width={windowDimensions.width}
          height={windowDimensions.height}
          recycle={false}
          numberOfPieces={200}
          colors={['#a855f7', '#c29dff', '#d8c4ff', '#f3edff', '#faf7ff']}
          gravity={0.1}
        />
      )}
      
      <div className="final-container">
        <motion.div 
          className="card-romantic"
          variants={containerVariants}
          initial="initial"
          animate="animate"
        >
          <motion.div
            variants={heartVariants}
            animate="animate"
            style={{ 
              fontSize: '4rem', 
              textAlign: 'center', 
              marginBottom: '24px',
              filter: 'drop-shadow(0 0 20px rgba(168, 85, 247, 0.5))'
            }}
          >
            💕
          </motion.div>

          <motion.h2 
            className="final-title title-script"
            variants={titleVariants}
            initial="initial"
            animate="animate"
          >
            Thank You, Beautiful 💕
          </motion.h2>
          
          <motion.p 
            className="final-message text-romantic"
            variants={messageVariants}
            initial="initial"
            animate="animate"
            style={{ fontSize: '1.2rem', textAlign: 'center', lineHeight: '1.7' }}
          >
            You've completed this little love quest, and every single one of your {totalAnswers} answers was absolutely perfect. 
            Each response revealed more of your beautiful soul and made this journey incredibly special.
          </motion.p>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9, duration: 0.6 }}
            style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              gap: '15px', 
              margin: '40px 0',
              fontSize: '2.5rem'
            }}
          >
            {['✨', '💫', '⭐', '💖', '🌟'].map((emoji, index) => (
              <motion.div
                key={index}
                animate={{
                  y: [0, -10, 0],
                  rotate: [0, 360],
                  scale: [1, 1.2, 1],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: index * 0.3,
                  ease: "easeInOut"
                }}
              >
                {emoji}
              </motion.div>
            ))}
          </motion.div>

          <motion.div
            variants={messageVariants}
            initial="initial"
            animate="animate"
            style={{ 
              background: 'var(--glass-lavender)',
              padding: '24px',
              borderRadius: '16px',
              margin: '32px 0',
              border: '1px solid rgba(168, 85, 247, 0.2)'
            }}
          >
            <p className="text-romantic" style={{ fontSize: '1.1rem', textAlign: 'center', fontStyle: 'italic' }}>
              "You'll never know who created this magical experience for you... 
              but he's the one who can't stop thinking about you, can't stop loving you, 
              and wanted to create something as beautiful as you are."
            </p>
          </motion.div>

          <motion.p 
            className="final-signature title-script"
            variants={signatureVariants}
            initial="initial"
            animate="animate"
            style={{ 
              fontSize: '1.6rem',
              textAlign: 'center',
              color: 'var(--lavender-600)',
              marginTop: '32px'
            }}
          >
            With all my love,<br />
            <motion.span
              animate={{
                color: ['var(--lavender-600)', 'var(--lavender-400)', 'var(--lavender-600)']
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              Your Secret Admirer 💌
            </motion.span>
          </motion.p>

          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1.5, duration: 0.8 }}
            style={{ 
              textAlign: 'center', 
              marginTop: '40px',
              fontSize: '3rem'
            }}
          >
            <motion.div
              animate={{
                scale: [1, 1.3, 1],
                rotate: [0, 360],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <FaHeart style={{ color: 'var(--lavender-400)' }} />
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}

export default FinalScreen
