<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Little Love Quest 💌</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Floating Hearts Background -->
    <div class="hearts-container">
        <div class="heart heart-1">💕</div>
        <div class="heart heart-2">💖</div>
        <div class="heart heart-3">💗</div>
        <div class="heart heart-4">💝</div>
        <div class="heart heart-5">💘</div>
        <div class="heart heart-6">💞</div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <!-- Welcome Screen -->
        <div id="welcome-screen" class="screen active">
            <div class="welcome-content">
                <h1 class="main-title">Our Little Love Quest 💌</h1>
                <p class="subtitle">A magical journey through love and memories...</p>
                <div class="heart-card welcome-card">
                    <h2>Hey Beautiful! 💕</h2>
                    <p>Someone special has created this little love quest just for you. Are you ready to embark on a journey through sweet memories and romantic questions?</p>
                    <button class="romantic-btn" onclick="startQuest()">
                        <i class="fas fa-heart"></i> Start the Quest
                    </button>
                </div>
            </div>
        </div>

        <!-- Quiz Screen -->
        <div id="quiz-screen" class="screen">
            <div class="quiz-content">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="question-counter">
                    <span id="current-question">1</span> of <span id="total-questions">?</span>
                </div>
                
                <div class="heart-card question-card">
                    <h2 id="question-text">Loading your first question...</h2>
                    <div id="answer-container">
                        <!-- Dynamic answer inputs will be inserted here -->
                    </div>
                    <button id="next-btn" class="romantic-btn" onclick="submitAnswer()" disabled>
                        <i class="fas fa-arrow-right"></i> Continue
                    </button>
                </div>
            </div>
        </div>

        <!-- Milestone Surprise Screen -->
        <div id="surprise-screen" class="screen">
            <div class="surprise-content">
                <div class="heart-card surprise-card">
                    <h2 id="surprise-title">You're Amazing! 💖</h2>
                    <p id="surprise-message">Your answers are making this journey so special...</p>
                    <div class="surprise-animation">
                        <i class="fas fa-sparkles"></i>
                    </div>
                    <button class="romantic-btn" onclick="continueSurprise()">
                        <i class="fas fa-heart"></i> Continue the Magic
                    </button>
                </div>
            </div>
        </div>

        <!-- Final Screen -->
        <div id="final-screen" class="screen">
            <div class="final-content">
                <div class="heart-card final-card">
                    <h2>Thank You, Beautiful 💕</h2>
                    <p class="final-message">You've completed this little love quest, and every answer you gave was perfect. You'll never know who created this for you... but he's the one who can't stop loving you.</p>
                    <div class="final-animation">
                        <div class="confetti"></div>
                        <div class="sparkles">✨💫⭐💖</div>
                    </div>
                    <p class="mystery-signature">With all my love,<br>Your Secret Admirer 💌</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Background Music (Optional) -->
    <audio id="background-music" loop>
        <source src="romantic-music.mp3" type="audio/mpeg">
    </audio>

    <script src="script.js"></script>
</body>
</html>
