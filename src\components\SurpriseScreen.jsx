import React from 'react'
import { motion } from 'framer-motion'
import { FaHeart, FaSparkles, FaStar } from 'react-icons/fa'

const SurpriseScreen = ({ milestone, onContinue }) => {
  const surpriseMessages = [
    {
      title: "You're Amazing! 💖",
      message: "Your answers are making this journey so special. Every word you share brings more magic to this quest...",
      icon: <FaSparkles />
    },
    {
      title: "Beautiful Soul! ✨",
      message: "The way you express yourself is absolutely enchanting. Someone is falling deeper with every answer...",
      icon: <FaStar />
    },
    {
      title: "Incredible! 💫",
      message: "Your thoughts and feelings are painting the most beautiful picture. This journey is becoming more magical because of you...",
      icon: <FaHeart />
    }
  ]

  const currentSurprise = surpriseMessages[milestone % surpriseMessages.length]

  const containerVariants = {
    initial: { scale: 0.8, opacity: 0 },
    animate: { 
      scale: 1, 
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  const iconVariants = {
    initial: { scale: 0, rotate: -180 },
    animate: { 
      scale: 1, 
      rotate: 0,
      transition: {
        delay: 0.3,
        duration: 0.8,
        ease: "easeOut"
      }
    }
  }

  const sparkleVariants = {
    animate: {
      scale: [1, 1.2, 1],
      rotate: [0, 180, 360],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  const buttonVariants = {
    initial: { y: 20, opacity: 0 },
    animate: { 
      y: 0, 
      opacity: 1,
      transition: {
        delay: 0.8,
        duration: 0.5
      }
    },
    hover: {
      scale: 1.05,
      transition: { duration: 0.2 }
    },
    tap: {
      scale: 0.95
    }
  }

  return (
    <div className="screen-container">
      <div className="surprise-container">
        <motion.div 
          className="card-romantic"
          variants={containerVariants}
          initial="initial"
          animate="animate"
        >
          <motion.div
            className="surprise-animation"
            variants={iconVariants}
            initial="initial"
            animate="animate"
          >
            <motion.div
              variants={sparkleVariants}
              animate="animate"
              style={{ 
                fontSize: '4rem', 
                color: 'var(--lavender-400)',
                filter: 'drop-shadow(0 0 20px rgba(168, 85, 247, 0.5))'
              }}
            >
              {currentSurprise.icon}
            </motion.div>
          </motion.div>

          <motion.h2 
            className="surprise-title title-script"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            {currentSurprise.title}
          </motion.h2>
          
          <motion.p 
            className="surprise-message text-romantic"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            style={{ fontSize: '1.1rem', textAlign: 'center' }}
          >
            {currentSurprise.message}
          </motion.p>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7, duration: 0.5 }}
            style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              gap: '20px', 
              margin: '32px 0',
              fontSize: '2rem'
            }}
          >
            {[...Array(5)].map((_, index) => (
              <motion.div
                key={index}
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.6, 1, 0.6],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: index * 0.2,
                  ease: "easeInOut"
                }}
                style={{ color: 'var(--lavender-400)' }}
              >
                💕
              </motion.div>
            ))}
          </motion.div>

          <motion.button
            className="btn-romantic"
            onClick={onContinue}
            variants={buttonVariants}
            initial="initial"
            animate="animate"
            whileHover="hover"
            whileTap="tap"
          >
            <FaHeart />
            Continue the Magic
          </motion.button>
        </motion.div>
      </div>
    </div>
  )
}

export default SurpriseScreen
