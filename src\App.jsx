import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import WelcomeScreen from './components/WelcomeScreen'
import QuizScreen from './components/QuizScreen'
import SurpriseScreen from './components/SurpriseScreen'
import FinalScreen from './components/FinalScreen'
import FloatingHearts from './components/FloatingHearts'
import BackgroundMusic from './components/BackgroundMusic'
import { questionsData } from './data/questions'
import { submitAnswerToSheet } from './utils/dataCollection'
import './App.css'

function App() {
  const [currentScreen, setCurrentScreen] = useState('welcome')
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState({})
  const [questionsAnswered, setQuestionsAnswered] = useState(0)
  const [currentQuestions, setCurrentQuestions] = useState(questionsData.initial)
  const [showSurprise, setShowSurprise] = useState(false)

  const handleStartQuest = () => {
    setCurrentScreen('quiz')
  }

  const handleAnswerSubmit = async (answer) => {
    const currentQuestion = currentQuestions[currentQuestionIndex]
    const newAnswers = {
      ...answers,
      [currentQuestion.id]: {
        question: currentQuestion.text,
        answer: answer,
        timestamp: new Date().toISOString()
      }
    }
    
    setAnswers(newAnswers)
    setQuestionsAnswered(prev => prev + 1)

    // Submit answer to collection system
    try {
      await submitAnswerToSheet({
        questionId: currentQuestion.id,
        question: currentQuestion.text,
        answer: answer,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.log('Answer collection failed, but continuing...', error)
    }

    // Check for surprise milestones
    if ((questionsAnswered + 1) % 4 === 0 && questionsAnswered < currentQuestions.length - 1) {
      setShowSurprise(true)
      setCurrentScreen('surprise')
      return
    }

    // Check if quiz is complete
    if (currentQuestionIndex >= currentQuestions.length - 1) {
      setCurrentScreen('final')
      return
    }

    // Move to next question
    setCurrentQuestionIndex(prev => prev + 1)
  }

  const handleContinueFromSurprise = () => {
    setShowSurprise(false)
    setCurrentScreen('quiz')
    setCurrentQuestionIndex(prev => prev + 1)
  }

  const screenVariants = {
    initial: { opacity: 0, y: 50, scale: 0.9 },
    animate: { opacity: 1, y: 0, scale: 1 },
    exit: { opacity: 0, y: -50, scale: 0.9 }
  }

  const transition = {
    duration: 0.6,
    ease: "easeInOut"
  }

  return (
    <div className="app">
      <FloatingHearts />
      <BackgroundMusic />
      
      <div className="app-container">
        <AnimatePresence mode="wait">
          {currentScreen === 'welcome' && (
            <motion.div
              key="welcome"
              variants={screenVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={transition}
            >
              <WelcomeScreen onStart={handleStartQuest} />
            </motion.div>
          )}

          {currentScreen === 'quiz' && (
            <motion.div
              key="quiz"
              variants={screenVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={transition}
            >
              <QuizScreen
                question={currentQuestions[currentQuestionIndex]}
                questionNumber={currentQuestionIndex + 1}
                totalQuestions={currentQuestions.length}
                onAnswerSubmit={handleAnswerSubmit}
                progress={(currentQuestionIndex / currentQuestions.length) * 100}
              />
            </motion.div>
          )}

          {currentScreen === 'surprise' && (
            <motion.div
              key="surprise"
              variants={screenVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={transition}
            >
              <SurpriseScreen
                milestone={Math.floor(questionsAnswered / 4)}
                onContinue={handleContinueFromSurprise}
              />
            </motion.div>
          )}

          {currentScreen === 'final' && (
            <motion.div
              key="final"
              variants={screenVariants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={transition}
            >
              <FinalScreen totalAnswers={questionsAnswered} />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

export default App
